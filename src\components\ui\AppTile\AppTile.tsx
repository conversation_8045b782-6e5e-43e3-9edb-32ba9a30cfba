import React, { useState, useRef, useEffect } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import { Modal } from '../Modal';

export interface AppTileProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
  color?: string; // Primary color for the app
  gradient?: boolean; // Whether to use gradient background for icon
  isActive?: boolean; // Whether the app is active/available
  isPremium?: boolean; // Whether this is a premium app
}

const AppTile: React.FC<AppTileProps> = ({
  title,
  description,
  icon,
  onClick,
  disabled = false,
  className = '',
  'data-testid': testId,
  color = '#f97316', // Default orange color
  gradient = true,
  isActive = true,
  isPremium = false,
}) => {
  const { colors, isDark } = useThemeStore();
  const [showInfoModal, setShowInfoModal] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const tileRef = useRef<HTMLDivElement>(null);
  const infoButtonRef = useRef<HTMLButtonElement>(null);

  const handleClick = () => {
    if (!disabled && onClick) {
      onClick();
    }
  };

  const isInactive = !isActive;
  
  const baseClasses = cn(
    'relative group cursor-pointer',
    'transition-all duration-300 ease-out',
    'focus:outline-none',
    'flex flex-col items-center justify-center text-center',
    'px-2 py-4 min-h-[100px]',
    disabled && 'opacity-50 cursor-not-allowed pointer-events-none',
    isInactive && 'opacity-60',
    className
  );

  // Card styles for icon container
  const iconCardStyles = {
    backgroundColor: 'transparent',
    transition: 'all 0.3s ease-out',
  };

  const iconCardHoverStyles = {
    transform: 'translateY(-2px) scale(1.02)',
    boxShadow: isDark
      ? `0 12px 30px -8px ${color}30, 0 8px 20px -5px rgba(0, 0, 0, 0.4)`
      : `0 12px 30px -8px ${color}20, 0 8px 20px -5px rgba(0, 0, 0, 0.1)`,
    backgroundColor: isDark
      ? 'rgba(30, 41, 59, 0.95)'
      : 'rgba(248, 250, 252, 1)',
    borderRadius: '16px',
  };

  // Create gradient background for icon
  const getIconBackground = () => {
    if (!gradient) return color;

    // Create gradient based on the primary color
    const baseColor = color;
    const lighterColor = adjustColorBrightness(baseColor, 20);
    const darkerColor = adjustColorBrightness(baseColor, -20);

    return `linear-gradient(135deg, ${lighterColor} 0%, ${baseColor} 50%, ${darkerColor} 100%)`;
  };

  // Helper function to adjust color brightness
  const adjustColorBrightness = (hex: string, percent: number) => {
    const num = parseInt(hex.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = ((num >> 8) & 0x00ff) + amt;
    const B = (num & 0x0000ff) + amt;
    return (
      '#' +
      (
        0x1000000 +
        (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
        (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
        (B < 255 ? (B < 1 ? 0 : B) : 255)
      )
        .toString(16)
        .slice(1)
    );
  };

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleInfoClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent tile click
    setShowInfoModal(true);
  };

  const handleTileClick = () => {
    if (!disabled && isActive && onClick) {
      onClick();
    }
  };

  const handleCloseInfoModal = () => {
    setShowInfoModal(false);
  };

  return (
    <>
      <div
        ref={tileRef}
        className={baseClasses}
        onClick={handleTileClick}
        data-testid={testId}
        tabIndex={disabled ? -1 : 0}
        role="button"
        aria-disabled={disabled}
      >
        {/* Premium Badge */}
        {isPremium && (
          <div className="absolute top-2 right-2 z-20">
            <div className="bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
              ⭐ Premium
            </div>
          </div>
        )}

        <div className="relative z-10 flex flex-col items-center text-center space-y-3 sm:space-y-4">
          {/* Icon with card effect */}
          {icon && (
            <div className="relative">
              <div
                className={cn(
                  "relative flex items-center justify-center transition-all duration-300",
                  "text-4xl sm:text-5xl p-4 rounded-2xl",
                  !isInactive && "group-hover:scale-105",
                  isInactive && "grayscale opacity-50"
                )}
                style={iconCardStyles}
                onMouseEnter={e => {
                  if (!disabled && !isInactive) {
                    Object.assign(e.currentTarget.style, iconCardHoverStyles);
                  }
                }}
                onMouseLeave={e => {
                  if (!disabled && !isInactive) {
                    Object.assign(e.currentTarget.style, iconCardStyles);
                  }
                }}
              >
                <div style={{ color: color }}>
                  {icon}
                </div>
                
                {/* Info button - show for all apps with description */}
                {description && (
                  <button
                    ref={infoButtonRef}
                    onClick={handleInfoClick}
                    className={cn(
                      "absolute -top-1 -right-1 w-5 h-5 rounded-full",
                      "flex items-center justify-center text-xs",
                      "transition-all duration-200 hover:scale-110",
                      "focus:outline-none focus:ring-2 focus:ring-offset-1",
                      isDark ? "bg-slate-700 text-slate-300 hover:bg-slate-600 focus:ring-slate-500"
                             : "bg-gray-200 text-gray-600 hover:bg-gray-300 focus:ring-gray-400"
                    )}
                    aria-label={`Show description for ${title}`}
                    type="button"
                  >
                    <svg
                      width="10"
                      height="10"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="opacity-70"
                    >
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                    </svg>
                  </button>
                )}

                {/* Small highlight dot - only show for active apps without description */}
                {isActive && !description && (
                  <div
                    className="absolute -top-1 -right-1 w-3 h-3 rounded-full opacity-80"
                    style={{
                      background:
                        'linear-gradient(135deg, rgba(255,255,255,0.8), rgba(255,255,255,0.4))',
                    }}
                  />
                )}
              </div>
            </div>
          )}
        </div>

        {/* Title outside the card */}
        <div className="mt-2 space-y-1">
          <h3
            className={cn(
              "font-semibold leading-tight text-sm sm:text-base",
              isInactive && "opacity-70"
            )}
            style={{ color: colors.text }}
          >
            {title}
          </h3>
        </div>
      </div>

      {/* Info Modal using reusable Modal component */}
      {description && (
        <Modal
          isOpen={showInfoModal}
          onClose={handleCloseInfoModal}
          title={title}
          size="lg"
        >
          <div className="px-8 py-6 space-y-8">
            {/* App Header */}
            <div className="flex items-center space-x-5">
              <div
                className="flex items-center justify-center w-16 h-16 rounded-2xl flex-shrink-0 shadow-lg"
                style={{
                  background: `linear-gradient(135deg, ${color}20, ${color}10)`,
                  border: `1px solid ${color}30`,
                  color: color,
                }}
              >
                <div className="text-2xl">
                  {icon}
                </div>
              </div>
              <div className="min-w-0 flex-1">
                <p
                  className="text-base opacity-70"
                  style={{ color: colors.textSecondary }}
                >
                  Application Details & Information
                </p>
              </div>
            </div>

            {/* Description */}
            <div>
              <h4
                className="text-lg font-semibold mb-4 flex items-center gap-2"
                style={{ color: colors.text }}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor" style={{ color: color }}>
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                Description
              </h4>
              <p
                className="text-lg leading-relaxed"
                style={{ color: colors.text }}
              >
                {description}
              </p>
            </div>

            {/* Application Details */}
            <div className="space-y-5">
              <h4
                className="text-lg font-semibold flex items-center gap-2"
                style={{ color: colors.text }}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor" style={{ color: color }}>
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                Application Details
              </h4>
              
              <div className="grid gap-4">
                <div
                  className="flex justify-between items-center py-3 px-4 rounded-xl"
                  style={{
                    backgroundColor: isDark ? 'rgba(71, 85, 105, 0.2)' : 'rgba(248, 250, 252, 0.8)',
                  }}
                >
                  <span
                    className="text-base font-medium"
                    style={{ color: colors.textSecondary }}
                  >
                    Status
                  </span>
                  <span
                    className={cn(
                      'text-sm font-semibold px-4 py-2 rounded-full shadow-sm',
                      isActive
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
                    )}
                  >
                    {isActive ? '✓ Active' : 'ℹ️ Inactive'}
                  </span>
                </div>
                
                {isPremium && (
                  <div
                    className="flex justify-between items-center py-3 px-4 rounded-xl"
                    style={{
                      backgroundColor: isDark ? 'rgba(71, 85, 105, 0.2)' : 'rgba(248, 250, 252, 0.8)',
                    }}
                  >
                    <span
                      className="text-base font-medium"
                      style={{ color: colors.textSecondary }}
                    >
                      Subscription
                    </span>
                    <span className="text-sm font-semibold px-4 py-2 rounded-full bg-gradient-to-r from-red-500 to-red-600 text-white shadow-sm">
                      ⭐ Premium
                    </span>
                  </div>
                )}

                <div
                  className="flex justify-between items-center py-3 px-4 rounded-xl"
                  style={{
                    backgroundColor: isDark ? 'rgba(71, 85, 105, 0.2)' : 'rgba(248, 250, 252, 0.8)',
                  }}
                >
                  <span
                    className="text-base font-medium"
                    style={{ color: colors.textSecondary }}
                  >
                    Category
                  </span>
                  <span
                    className="text-sm font-semibold px-4 py-2 rounded-full shadow-sm"
                    style={{
                      backgroundColor: `${color}20`,
                      color: color,
                    }}
                  >
                    Business Application
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <button
                onClick={() => {
                  handleCloseInfoModal();
                  handleTileClick();
                }}
                disabled={disabled || !isActive}
                className={cn(
                  'flex-1 py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-300',
                  'focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-lg',
                  disabled || !isActive
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]'
                )}
                style={{
                  background: disabled || !isActive
                    ? colors.textSecondary + '20'
                    : `linear-gradient(135deg, ${color}, ${adjustColorBrightness(color, -10)})`,
                  color: disabled || !isActive ? colors.textSecondary : 'white',
                }}
              >
                {disabled ? '🚫 Unavailable' : !isActive ? 'ℹ️ Contact Support' : `🚀 Launch ${title}`}
              </button>
              
              <button
                onClick={handleCloseInfoModal}
                className={cn(
                  'px-6 py-4 rounded-xl font-medium text-base transition-all duration-200',
                  'focus:outline-none focus:ring-2 focus:ring-offset-2',
                  'hover:scale-105 active:scale-95',
                  isDark
                    ? 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                )}
              >
                Close
              </button>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default AppTile;
