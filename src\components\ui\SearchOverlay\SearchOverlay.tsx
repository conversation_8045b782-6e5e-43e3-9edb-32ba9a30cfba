import React, { useState, useEffect, useRef } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface SearchResult {
  id: string;
  title: string;
  category: string;
  path: string;
  icon?: string;
}

export interface SearchOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect?: (result: SearchResult) => void;
  placeholder?: string;
  results?: SearchResult[];
  className?: string;
  'data-testid'?: string;
}

const SearchOverlay: React.FC<SearchOverlayProps> = ({
  isOpen,
  onClose,
  onSelect,
  placeholder = 'Search…',
  results = [],
  className = '',
  'data-testid': testId,
}) => {
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const { colors, isDark } = useThemeStore();
  const inputRef = useRef<HTMLInputElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);

  // Default search results
  const defaultResults: SearchResult[] = [
    {
      id: '1',
      title: 'Helpdesk Teams',
      category: 'Helpdesk / Configuration',
      path: '/helpdesk/teams',
    },
    {
      id: '2',
      title: 'Channels',
      category: 'Live Chat',
      path: '/chat/channels',
    },
    {
      id: '3',
      title: 'Email Channels',
      category: 'Settings / Technical / Email',
      path: '/settings/email/channels',
    },
    {
      id: '4',
      title: 'Scheduled Actions',
      category: 'Settings / Technical / Automation',
      path: '/settings/automation/scheduled',
    },
    {
      id: '5',
      title: 'Sales Orders Headers/Footers',
      category: 'Sales / Configuration / Sales Orders',
      path: '/sales/orders/headers',
    },
  ];

  const filteredResults =
    query.length > 0
      ? (results.length > 0 ? results : defaultResults).filter(
          result =>
            result.title.toLowerCase().includes(query.toLowerCase()) ||
            result.category.toLowerCase().includes(query.toLowerCase())
        )
      : results.length > 0
        ? results
        : defaultResults;

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    setSelectedIndex(0);
  }, [filteredResults]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Escape':
        onClose();
        break;
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev =>
          Math.min(prev + 1, filteredResults.length - 1)
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, 0));
        break;
      case 'Enter':
        e.preventDefault();
        if (filteredResults[selectedIndex]) {
          handleSelect(filteredResults[selectedIndex]);
        }
        break;
    }
  };

  const handleSelect = (result: SearchResult) => {
    onSelect?.(result);
    onClose();
    setQuery('');
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === overlayRef.current) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const overlayStyles = {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    backdropFilter: 'blur(4px)',
  };

  const modalStyles = {
    backgroundColor: isDark
      ? 'rgba(30, 41, 59, 0.95)'
      : 'rgba(248, 250, 252, 0.95)',
    backdropFilter: 'blur(12px)',
    border: `1px solid ${isDark ? 'rgba(71, 85, 105, 0.3)' : 'rgba(203, 213, 225, 0.5)'}`,
    boxShadow: isDark
      ? '0 25px 50px -12px rgba(0, 0, 0, 0.5)'
      : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  };

  return (
    <div
      ref={overlayRef}
      className={cn(
        'fixed inset-0 z-50 flex items-start justify-center pt-[10vh]',
        'animate-fade-in',
        className
      )}
      style={overlayStyles}
      onClick={handleBackdropClick}
      data-testid={testId}
    >
      <div
        className="w-full max-w-2xl mx-4 rounded-lg overflow-hidden animate-scale-in"
        style={modalStyles}
        onClick={e => e.stopPropagation()}
      >
        {/* Search Input */}
        <div className="p-4 border-b" style={{ borderColor: colors.border }}>
          <div className="relative">
            <input
              ref={inputRef}
              type="text"
              value={query}
              onChange={e => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className="w-full px-4 py-3 pr-12 text-lg bg-transparent border-none outline-none"
              style={{ color: colors.text }}
            />
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                style={{ color: colors.textSecondary }}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Results List */}
        <div className="max-h-96 overflow-y-auto">
          {filteredResults.map((result, index) => (
            <button
              key={result.id}
              onClick={() => handleSelect(result)}
              className={cn(
                'w-full px-4 py-3 text-left hover:bg-white/5 transition-colors duration-150',
                index === selectedIndex && 'bg-white/10'
              )}
              style={{
                backgroundColor:
                  index === selectedIndex
                    ? 'rgba(255, 255, 255, 0.1)'
                    : 'transparent',
              }}
            >
              <div className="flex items-center gap-3">
                {result.icon && (
                  <span className="text-xl flex-shrink-0">
                    {result.icon}
                  </span>
                )}
                <div className="flex flex-col">
                  <span className="font-medium" style={{ color: colors.text }}>
                    {result.title}
                  </span>
                  <span
                    className="text-sm opacity-70"
                    style={{ color: colors.textSecondary }}
                  >
                    {result.category}
                  </span>
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* Keyboard Hint */}
        <div
          className="px-4 py-3 border-t text-center text-sm"
          style={{
            borderColor: colors.border,
            color: colors.textSecondary,
          }}
        >
          TIP — open me anywhere with CTRL + K
        </div>
      </div>
    </div>
  );
};

export default SearchOverlay;
